// QR Code Scanner Implementation
class QRScanner {
    constructor() {
        this.html5QrCode = null;
        this.isScanning = false;
        this.lastScannedResult = null;

        this.initializeElements();
        this.initializeAudio();
        this.bindEvents();
    }

    initializeElements() {
        this.overlay = document.getElementById('qr-scanner-overlay');
        this.modal = document.getElementById('qr-scanner-modal');
        this.closeBtn = document.getElementById('qr-scanner-close');
        this.cancelBtn = document.getElementById('qr-scanner-cancel');
        this.okBtn = document.getElementById('qr-scanner-ok');
        this.readerDiv = document.getElementById('qr-reader');
        this.messageDiv = document.getElementById('qr-scanner-message');
        this.resultDiv = document.getElementById('qr-scanner-result');
        this.resultContent = document.getElementById('qr-result-content');
        this.qrFab = document.getElementById('qr-fab');
        this.testOption = document.getElementById('qr-test-option');
        this.testBtn = document.getElementById('qr-test-btn');
    }

    initializeAudio() {
        // Initialize scan sound
        this.scanSound = new Audio('assets/barcode.mp3');
        this.scanSound.preload = 'auto';
        this.scanSound.volume = 0.7; // Set volume to 70%

        // Handle audio loading errors gracefully
        this.scanSound.addEventListener('error', (e) => {
            console.warn('Could not load scan sound:', e);
            this.scanSound = null; // Disable sound if it fails to load
        });

        this.scanSound.addEventListener('canplaythrough', () => {
            console.log('Scan sound loaded successfully');
        });
    }

    bindEvents() {
        // QR FAB click event
        if (this.qrFab) {
            this.qrFab.addEventListener('click', () => this.openScanner());
        }

        // Close events
        this.closeBtn.addEventListener('click', () => this.closeScanner());
        this.cancelBtn.addEventListener('click', () => this.closeScanner());
        this.okBtn.addEventListener('click', () => this.closeScanner());
        
        // Overlay click to close
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.closeScanner();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.overlay.classList.contains('active')) {
                this.closeScanner();
            }
        });

        // Test button for development
        if (this.testBtn) {
            this.testBtn.addEventListener('click', () => this.testScan());
        }
    }

    async openScanner() {
        try {
            console.log('Opening QR scanner...');

            // Show the modal
            this.overlay.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Reset UI state
            this.resetUI();

            // Ensure we're starting fresh
            this.isScanning = false;
            this.lastScannedResult = null;

            // Initialize the scanner
            await this.initializeScanner();

        } catch (error) {
            console.error('Error opening QR scanner:', error);
            this.showError('Failed to access camera. Please check permissions.');
        }
    }

    async initializeScanner() {
        // Ensure complete cleanup before starting
        if (this.html5QrCode) {
            try {
                await this.stopScanner();
                await this.html5QrCode.clear();
            } catch (error) {
                console.warn('Error during cleanup:', error);
            }
            this.html5QrCode = null;
        }

        // Clear the reader div completely
        this.readerDiv.innerHTML = '';

        // Small delay to ensure DOM is ready
        await new Promise(resolve => setTimeout(resolve, 100));

        // Create new scanner instance
        this.html5QrCode = new Html5Qrcode("qr-reader");
        console.log('New scanner instance created');

        const config = {
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0,
            disableFlip: false,
            rememberLastUsedCamera: true
        };

        try {
            // Check if we're on HTTPS or localhost
            const isSecureContext = window.isSecureContext ||
                                  location.protocol === 'https:' ||
                                  location.hostname === 'localhost' ||
                                  location.hostname === '127.0.0.1';

            if (!isSecureContext) {
                throw new Error('HTTPS_REQUIRED');
            }

            // Check if getUserMedia is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('MEDIA_NOT_SUPPORTED');
            }

            // Try to get cameras
            const cameras = await Html5Qrcode.getCameras();

            if (cameras && cameras.length > 0) {
                console.log('Available cameras:', cameras);

                // Prefer back camera if available
                let cameraId = cameras[0].id;
                const backCamera = cameras.find(camera =>
                    camera.label.toLowerCase().includes('back') ||
                    camera.label.toLowerCase().includes('rear') ||
                    camera.label.toLowerCase().includes('environment')
                );

                if (backCamera) {
                    cameraId = backCamera.id;
                    console.log('Using back camera:', backCamera.label);
                } else {
                    console.log('Using first available camera:', cameras[0].label);
                }

                // Start scanning
                await this.html5QrCode.start(
                    cameraId,
                    config,
                    (decodedText, decodedResult) => {
                        this.onScanSuccess(decodedText, decodedResult);
                    },
                    (errorMessage) => {
                        // Handle scan errors silently - this is normal during scanning
                        // console.log('Scan error:', errorMessage);
                    }
                );

                this.isScanning = true;
                this.showMessage('Position the QR code within the frame to scan');

            } else {
                throw new Error('NO_CAMERAS');
            }

        } catch (error) {
            console.error('Error starting scanner:', error);
            this.handleScannerError(error);
        }
    }

    handleScannerError(error) {
        let errorMessage = 'Failed to start camera. ';
        let suggestions = '';

        if (error.message === 'HTTPS_REQUIRED') {
            errorMessage = 'Camera access requires HTTPS. ';
            suggestions = 'Please use HTTPS or localhost to access the camera.';
        } else if (error.message === 'MEDIA_NOT_SUPPORTED') {
            errorMessage = 'Camera not supported. ';
            suggestions = 'Your browser doesn\'t support camera access.';
        } else if (error.message === 'NO_CAMERAS') {
            errorMessage = 'No cameras found. ';
            suggestions = 'Please check if your device has a camera.';
        } else if (error.name === 'NotAllowedError' || error.message.includes('Permission')) {
            errorMessage = 'Camera permission denied. ';
            suggestions = 'Please allow camera access and try again.';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'No camera found. ';
            suggestions = 'Please check if your camera is connected and not being used by another application.';
        } else if (error.name === 'NotReadableError') {
            errorMessage = 'Camera is busy. ';
            suggestions = 'Please close other applications using the camera and try again.';
        } else {
            errorMessage = 'Camera error. ';
            suggestions = 'Please check permissions and try again.';
        }

        this.showError(errorMessage + suggestions);
    }

    async onScanSuccess(decodedText, decodedResult) {
        if (this.lastScannedResult === decodedText) {
            return; // Prevent duplicate scans
        }

        this.lastScannedResult = decodedText;

        try {
            // Play scan sound
            this.playScanSound();

            // Stop scanning
            await this.stopScanner();

            // Show success result
            this.showResult(decodedText);

            // Add haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(200);
            }

        } catch (error) {
            console.error('Error handling scan result:', error);
        }
    }

    playScanSound() {
        if (this.scanSound) {
            try {
                // Reset the audio to the beginning in case it was played before
                this.scanSound.currentTime = 0;

                // Play the sound
                const playPromise = this.scanSound.play();

                // Handle play promise (required for some browsers)
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.warn('Could not play scan sound:', error);
                        // This is common if user hasn't interacted with the page yet
                    });
                }

                console.log('Scan sound played');

            } catch (error) {
                console.warn('Error playing scan sound:', error);
            }
        }
    }

    async stopScanner() {
        if (this.html5QrCode && this.isScanning) {
            try {
                console.log('Stopping scanner...');
                await this.html5QrCode.stop();
                this.isScanning = false;
                console.log('Scanner stopped successfully');

                // Additional cleanup - stop all video tracks
                const video = this.readerDiv.querySelector('video');
                if (video && video.srcObject) {
                    const tracks = video.srcObject.getTracks();
                    tracks.forEach(track => {
                        track.stop();
                        console.log('Video track stopped');
                    });
                }

            } catch (error) {
                console.error('Error stopping scanner:', error);
                this.isScanning = false; // Reset state even if stop fails
            }
        }
    }

    async closeScanner() {
        try {
            // Stop the scanner first
            await this.stopScanner();

            // Clear the scanner instance completely
            if (this.html5QrCode) {
                try {
                    await this.html5QrCode.clear();
                } catch (clearError) {
                    console.warn('Error clearing scanner:', clearError);
                }
                this.html5QrCode = null;
            }

            // Clear the reader div completely
            this.readerDiv.innerHTML = '';

            // Hide the modal
            this.overlay.classList.remove('active');
            document.body.style.overflow = '';

            // Reset all state
            this.lastScannedResult = null;
            this.isScanning = false;

            console.log('Scanner closed and reset successfully');

        } catch (error) {
            console.error('Error closing scanner:', error);
            // Force close anyway
            this.forceCloseScanner();
        }
    }

    forceCloseScanner() {
        // Force close without waiting for async operations
        this.overlay.classList.remove('active');
        document.body.style.overflow = '';
        this.readerDiv.innerHTML = '';
        this.html5QrCode = null;
        this.isScanning = false;
        this.lastScannedResult = null;
        console.log('Scanner force closed');
    }

    resetUI() {
        console.log('Resetting UI...');

        this.messageDiv.style.display = 'block';
        this.resultDiv.style.display = 'none';
        this.okBtn.style.display = 'none';
        this.cancelBtn.textContent = 'Cancel';

        // Clear reader div completely
        this.readerDiv.innerHTML = '';

        // Remove any existing file upload options
        const existingFileOption = this.messageDiv.querySelector('.file-upload-option');
        if (existingFileOption) {
            existingFileOption.remove();
        }

        // Reset icon and text
        const iconElement = this.messageDiv.querySelector('.material-icons');
        const textElement = this.messageDiv.querySelector('.qr-scanner-text');
        if (iconElement) {
            iconElement.textContent = 'qr_code_scanner';
            iconElement.style.color = 'var(--primary-color)';
        }
        if (textElement) {
            textElement.textContent = 'Position the QR code within the frame to scan';
        }

        // Show test option if we're in development (localhost or file://)
        if (this.testOption) {
            const isDevelopment = location.hostname === 'localhost' ||
                                location.hostname === '127.0.0.1' ||
                                location.protocol === 'file:';
            this.testOption.style.display = isDevelopment ? 'block' : 'none';
        }

        console.log('UI reset complete');
    }

    showMessage(text) {
        const textElement = this.messageDiv.querySelector('.qr-scanner-text');
        if (textElement) {
            textElement.textContent = text;
        }
        this.messageDiv.style.display = 'block';
        this.resultDiv.style.display = 'none';
    }

    showResult(decodedText) {
        this.messageDiv.style.display = 'none';
        this.resultDiv.style.display = 'block';
        this.resultContent.textContent = decodedText;
        this.okBtn.style.display = 'inline-flex';
        this.cancelBtn.textContent = 'Close';
    }

    showError(message) {
        this.showMessage(message);
        const iconElement = this.messageDiv.querySelector('.material-icons');
        if (iconElement) {
            iconElement.textContent = 'error';
            iconElement.style.color = 'var(--error-color)';
        }

        // Add file upload option as fallback
        this.addFileUploadOption();
    }

    addFileUploadOption() {
        // Check if file upload option already exists
        if (this.messageDiv.querySelector('.file-upload-option')) {
            return;
        }

        const fileUploadHtml = `
            <div class="file-upload-option" style="margin-top: 20px;">
                <div style="margin-bottom: 12px; color: var(--text-secondary); font-size: 14px;">
                    Or scan QR code from image file:
                </div>
                <input type="file" id="qr-file-input" accept="image/*" style="display: none;">
                <button class="mdc-button mdc-button--outlined" id="qr-file-btn">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">photo</span>
                    <span class="mdc-button__label">Choose Image</span>
                </button>
            </div>
        `;

        this.messageDiv.insertAdjacentHTML('beforeend', fileUploadHtml);

        // Add event listeners
        const fileInput = document.getElementById('qr-file-input');
        const fileBtn = document.getElementById('qr-file-btn');

        fileBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.scanFromFile(file);
            }
        });
    }

    async scanFromFile(file) {
        try {
            this.showMessage('Scanning image...');

            if (!this.html5QrCode) {
                this.html5QrCode = new Html5Qrcode("qr-reader");
            }

            const result = await this.html5QrCode.scanFile(file, true);

            // Play sound and show result
            this.playScanSound();
            this.showResult(result);

            // Add haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(200);
            }

        } catch (error) {
            console.error('Error scanning file:', error);
            this.showMessage('No QR code found in the image. Please try another image.');
        }
    }

    testScan() {
        // Simulate a successful QR code scan for testing
        const testData = `Test QR Code - ${new Date().toLocaleString()}`;

        // Play sound first for immediate feedback
        this.playScanSound();

        // Then show the result
        this.showResult(testData);

        // Add haptic feedback if available
        if (navigator.vibrate) {
            navigator.vibrate(200);
        }
    }

    // Debug method to check scanner state
    getScannerState() {
        return {
            hasInstance: !!this.html5QrCode,
            isScanning: this.isScanning,
            lastResult: this.lastScannedResult,
            modalOpen: this.overlay.classList.contains('active')
        };
    }

    // Force reset method for debugging
    forceReset() {
        console.log('Force resetting scanner...');
        this.forceCloseScanner();
        this.resetUI();
        console.log('Force reset complete');
    }
}

// Initialize QR Scanner when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.qrScanner = new QRScanner();

    // Add debug methods to window for testing
    window.debugQRScanner = () => {
        console.log('QR Scanner State:', window.qrScanner.getScannerState());
    };

    window.resetQRScanner = () => {
        window.qrScanner.forceReset();
    };
});
