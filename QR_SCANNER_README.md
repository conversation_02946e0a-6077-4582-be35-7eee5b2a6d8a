# QR Code Scanner - Testing Guide

## Camera Permission Issues

The QR code scanner requires camera access, which has specific requirements:

### 🔒 HTTPS Requirement
Modern browsers require **HTTPS** for camera access (except for localhost). If you're getting "Failed to start camera" errors, this is likely the cause.

## 🚀 Quick Start Options

### Option 1: Local HTTPS Server (Recommended)
```bash
# Run the Python HTTPS server
python start-server.py

# Or on Windows, double-click:
start-server.bat
```

Then open: `https://localhost:8443/`

### Option 2: Simple HTTP Server (File Upload Only)
```bash
# Python 3
python -m http.server 8080

# Or Python 2
python -m SimpleHTTPServer 8080
```

Then open: `http://localhost:8080/`

**Note:** Camera won't work with HTTP, but you can use the "Choose Image" option.

### Option 3: Development Test Mode
When running locally (localhost or file://), you'll see a "Test QR Scan" button that simulates a successful scan.

## 📱 Testing the QR Scanner

### On Mobile:
1. Look for the QR code floating action button (blue circle with QR icon)
2. Tap it to open the scanner
3. Point camera at a QR code
4. <PERSON>anner will automatically detect and show the result

### On Desktop:
- QR scanner is hidden on desktop (mobile-first design)
- You can test by resizing browser window to mobile size (≤768px width)

## 🔧 Troubleshooting

### "Failed to start camera" Error:
1. **Check HTTPS**: Use `https://localhost` instead of `file://`
2. **Allow Permissions**: Click "Allow" when browser asks for camera access
3. **Check Camera**: Ensure camera isn't being used by another app
4. **Try File Upload**: Use "Choose Image" option as fallback

### Camera Not Found:
1. Check if your device has a camera
2. Ensure camera drivers are installed
3. Try refreshing the page
4. Check browser console for detailed error messages

### Browser Compatibility:
- ✅ Chrome/Edge (recommended)
- ✅ Firefox
- ✅ Safari (iOS 15.1+)
- ❌ Internet Explorer (not supported)

## 🎯 Test QR Codes

You can test with these online QR code generators:
- [QR Code Generator](https://www.qr-code-generator.com/)
- [QRCode Monkey](https://www.qrcode-monkey.com/)

Or create a simple text QR code with content like:
- "Hello World"
- "https://example.com"
- "Student ID: *********"

## 🔍 Features

### Camera Scanner:
- ✅ Auto-detect QR codes
- ✅ Prefer back camera on mobile
- ✅ Clean Material Design interface
- ✅ Success/error feedback
- ✅ Haptic feedback (if supported)

### File Upload Fallback:
- ✅ Upload image files
- ✅ Scan QR codes from photos
- ✅ Works without camera permissions

### Development Mode:
- ✅ Test button for development
- ✅ Simulated scan results
- ✅ Available on localhost/file://

## 🎨 Design Features

- **Material Design**: Consistent with app design
- **Responsive**: Mobile-first approach
- **Dark Mode**: Supports app's dark theme
- **Clean Interface**: Minimal, focused UI
- **Error Handling**: Helpful error messages
- **Accessibility**: Proper ARIA labels and keyboard support

## 📝 Implementation Details

### Files:
- `js/qr-scanner.js` - Main scanner logic
- `index.html` - QR scanner modal HTML
- `styles.css` - QR scanner styling

### Dependencies:
- [html5-qrcode](https://github.com/mebjas/html5-qrcode) - QR scanning library
- Material Design Components - UI framework
- Alpine.js - Reactive framework

### Browser APIs Used:
- `navigator.mediaDevices.getUserMedia()` - Camera access
- `FileReader` - File upload scanning
- `navigator.vibrate()` - Haptic feedback (optional)

## 🔄 Next Steps

The QR scanner is ready for integration with your student management system. You can:

1. **Process Scan Results**: Modify `onScanSuccess()` to handle student IDs
2. **Add Validation**: Validate QR code format before processing
3. **Database Integration**: Look up students by scanned ID
4. **Custom Actions**: Add student-specific actions after scan

Example integration:
```javascript
onScanSuccess(decodedText, decodedResult) {
    // Check if it's a student ID
    if (decodedText.startsWith('ST2024')) {
        // Look up student in database
        const student = findStudentById(decodedText);
        if (student) {
            // Show student details or perform action
            showStudentDetails(student);
        }
    }
    // Show generic result
    this.showResult(decodedText);
}
```
