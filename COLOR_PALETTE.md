# Color Palette System

This document explains how to use and customize the color palette for the Student Management System.

## Overview

The application uses CSS Custom Properties (CSS Variables) to create a centralized color system. This allows you to change colors in one place and have them reflected throughout the entire application.

## Color Variables

### Primary Colors
```css
--primary-color: #6200ea;        /* Main brand color */
--primary-light: #7c4dff;        /* Lighter variant */
--primary-dark: #5500d4;         /* Darker variant */
--primary-gradient: linear-gradient(135deg, var(--primary-color), var(--primary-light));
```

### Secondary Colors
```css
--secondary-color: #03dac6;      /* Accent color */
--secondary-light: #66fff9;      /* Lighter accent */
--secondary-dark: #00a896;       /* Darker accent */
```

### Background Colors
```css
--background-primary: #f5f5f5;   /* Main page background */
--background-secondary: #fafafa; /* Secondary surfaces */
--background-surface: #ffffff;   /* Cards, modals, etc. */
--background-overlay: rgba(255, 255, 255, 0.9); /* Overlay backgrounds */
```

### Text Colors
```css
--text-primary: #212121;         /* Main text */
--text-secondary: #757575;       /* Secondary text */
--text-disabled: #9e9e9e;        /* Disabled text */
--text-on-primary: #ffffff;      /* Text on primary color */
--text-on-surface: #212121;      /* Text on white surfaces */
```

### Border Colors
```css
--border-light: #e0e0e0;         /* Light borders */
--border-medium: #bdbdbd;        /* Medium borders */
--border-dark: #9e9e9e;          /* Dark borders */
```

### State Colors
```css
--success-color: #4caf50;        /* Success states */
--warning-color: #ff9800;        /* Warning states */
--error-color: #f44336;          /* Error states */
--info-color: #2196f3;           /* Info states */
```

### Surface Colors
```css
--surface-hover: #f5f5f5;        /* Hover states */
--surface-selected: #e8eaf6;     /* Selected states */
--surface-disabled: #f5f5f5;     /* Disabled states */
```

### Shadow Colors
```css
--shadow-light: rgba(0, 0, 0, 0.1);   /* Light shadows */
--shadow-medium: rgba(0, 0, 0, 0.2);  /* Medium shadows */
--shadow-dark: rgba(0, 0, 0, 0.3);    /* Dark shadows */
```

### Primary Color with Opacity
```css
--primary-alpha-8: rgba(98, 0, 234, 0.08);   /* 8% opacity */
--primary-alpha-12: rgba(98, 0, 234, 0.12);  /* 12% opacity */
--primary-alpha-16: rgba(98, 0, 234, 0.16);  /* 16% opacity */
--primary-alpha-24: rgba(98, 0, 234, 0.24);  /* 24% opacity */
```

## How to Customize Colors

### Method 1: Edit CSS Variables Directly
1. Open `styles.css` or `login-styles.css`
2. Find the `:root` section at the top
3. Change the color values as needed

Example - Change to a blue theme:
```css
:root {
    --primary-color: #1976d2;        /* Blue */
    --primary-light: #42a5f5;        /* Light blue */
    --primary-dark: #1565c0;         /* Dark blue */
    /* ... other colors remain the same ... */
}
```

### Method 2: Override with Additional CSS
Add this to the end of your CSS file:
```css
:root {
    --primary-color: #e91e63;        /* Pink theme */
    --primary-light: #f06292;
    --primary-dark: #c2185b;
}
```

### Method 3: Theme Switching (Advanced)
Create multiple theme classes:
```css
.theme-blue {
    --primary-color: #2196f3;
    --primary-light: #64b5f6;
    --primary-dark: #1976d2;
}

.theme-green {
    --primary-color: #4caf50;
    --primary-light: #81c784;
    --primary-dark: #388e3c;
}
```

Then apply the theme class to the body element via JavaScript.

## Popular Color Themes

### Material Design Colors
- **Purple (Current)**: `#6200ea`, `#7c4dff`, `#5500d4`
- **Blue**: `#2196f3`, `#64b5f6`, `#1976d2`
- **Green**: `#4caf50`, `#81c784`, `#388e3c`
- **Orange**: `#ff9800`, `#ffb74d`, `#f57c00`
- **Red**: `#f44336`, `#ef5350`, `#d32f2f`
- **Teal**: `#009688`, `#4db6ac`, `#00796b`

### Usage Examples

```css
/* Using variables in your CSS */
.my-button {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border: 1px solid var(--border-light);
}

.my-card {
    background-color: var(--background-surface);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.my-text {
    color: var(--text-primary);
}
```

## Files Using the Color System

- `styles.css` - Main application styles
- `login-styles.css` - Login page styles

Both files include the complete color palette at the top in the `:root` section.

## Benefits

1. **Consistency**: All colors are defined in one place
2. **Easy Theming**: Change the entire app's color scheme quickly
3. **Maintainability**: No need to search and replace colors throughout files
4. **Flexibility**: Easy to create multiple themes or dark mode
5. **Future-Proof**: Easy to add new color variations

## Browser Support

CSS Custom Properties are supported in all modern browsers:
- Chrome 49+
- Firefox 31+
- Safari 9.1+
- Edge 16+
